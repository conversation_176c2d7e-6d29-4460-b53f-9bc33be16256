# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/cmake/linux/x64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/cmake/linux/x64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/orapp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/orapp/cmake-build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/cmake/linux/x64/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/orapp/cmake-build/CMakeFiles /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/orapp/cmake-build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/orapp/cmake-build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named liborapp

# Build rule for target.
liborapp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 liborapp
.PHONY : liborapp

# fast build rule for target.
liborapp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/build
.PHONY : liborapp/fast

#=============================================================================
# Target rules for targets named orapp_test

# Build rule for target.
orapp_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 orapp_test
.PHONY : orapp_test

# fast build rule for target.
orapp_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/orapp_test.dir/build.make CMakeFiles/orapp_test.dir/build
.PHONY : orapp_test/fast

#=============================================================================
# Target rules for targets named install_orapp

# Build rule for target.
install_orapp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 install_orapp
.PHONY : install_orapp

# fast build rule for target.
install_orapp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/install_orapp.dir/build.make CMakeFiles/install_orapp.dir/build
.PHONY : install_orapp/fast

#=============================================================================
# Target rules for targets named clean_orapp

# Build rule for target.
clean_orapp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean_orapp
.PHONY : clean_orapp

# fast build rule for target.
clean_orapp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean_orapp.dir/build.make CMakeFiles/clean_orapp.dir/build
.PHONY : clean_orapp/fast

conn.o: conn.cc.o
.PHONY : conn.o

# target to build an object file
conn.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/conn.cc.o
.PHONY : conn.cc.o

conn.i: conn.cc.i
.PHONY : conn.i

# target to preprocess a source file
conn.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/conn.cc.i
.PHONY : conn.cc.i

conn.s: conn.cc.s
.PHONY : conn.s

# target to generate assembly for a file
conn.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/conn.cc.s
.PHONY : conn.cc.s

constants.o: constants.cc.o
.PHONY : constants.o

# target to build an object file
constants.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/constants.cc.o
.PHONY : constants.cc.o

constants.i: constants.cc.i
.PHONY : constants.i

# target to preprocess a source file
constants.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/constants.cc.i
.PHONY : constants.cc.i

constants.s: constants.cc.s
.PHONY : constants.s

# target to generate assembly for a file
constants.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/constants.cc.s
.PHONY : constants.cc.s

error.o: error.cc.o
.PHONY : error.o

# target to build an object file
error.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/error.cc.o
.PHONY : error.cc.o

error.i: error.cc.i
.PHONY : error.i

# target to preprocess a source file
error.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/error.cc.i
.PHONY : error.cc.i

error.s: error.cc.s
.PHONY : error.s

# target to generate assembly for a file
error.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/error.cc.s
.PHONY : error.cc.s

field.o: field.cc.o
.PHONY : field.o

# target to build an object file
field.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/field.cc.o
.PHONY : field.cc.o

field.i: field.cc.i
.PHONY : field.i

# target to preprocess a source file
field.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/field.cc.i
.PHONY : field.cc.i

field.s: field.cc.s
.PHONY : field.s

# target to generate assembly for a file
field.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/field.cc.s
.PHONY : field.cc.s

log.o: log.cc.o
.PHONY : log.o

# target to build an object file
log.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/log.cc.o
.PHONY : log.cc.o

log.i: log.cc.i
.PHONY : log.i

# target to preprocess a source file
log.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/log.cc.i
.PHONY : log.cc.i

log.s: log.cc.s
.PHONY : log.s

# target to generate assembly for a file
log.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/log.cc.s
.PHONY : log.cc.s

query.o: query.cc.o
.PHONY : query.o

# target to build an object file
query.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/query.cc.o
.PHONY : query.cc.o

query.i: query.cc.i
.PHONY : query.i

# target to preprocess a source file
query.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/query.cc.i
.PHONY : query.cc.i

query.s: query.cc.s
.PHONY : query.s

# target to generate assembly for a file
query.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/query.cc.s
.PHONY : query.cc.s

row.o: row.cc.o
.PHONY : row.o

# target to build an object file
row.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/row.cc.o
.PHONY : row.cc.o

row.i: row.cc.i
.PHONY : row.i

# target to preprocess a source file
row.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/row.cc.i
.PHONY : row.cc.i

row.s: row.cc.s
.PHONY : row.s

# target to generate assembly for a file
row.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/liborapp.dir/build.make CMakeFiles/liborapp.dir/row.cc.s
.PHONY : row.cc.s

test.o: test.cc.o
.PHONY : test.o

# target to build an object file
test.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/orapp_test.dir/build.make CMakeFiles/orapp_test.dir/test.cc.o
.PHONY : test.cc.o

test.i: test.cc.i
.PHONY : test.i

# target to preprocess a source file
test.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/orapp_test.dir/build.make CMakeFiles/orapp_test.dir/test.cc.i
.PHONY : test.cc.i

test.s: test.cc.s
.PHONY : test.s

# target to generate assembly for a file
test.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/orapp_test.dir/build.make CMakeFiles/orapp_test.dir/test.cc.s
.PHONY : test.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... clean_orapp"
	@echo "... install_orapp"
	@echo "... liborapp"
	@echo "... orapp_test"
	@echo "... conn.o"
	@echo "... conn.i"
	@echo "... conn.s"
	@echo "... constants.o"
	@echo "... constants.i"
	@echo "... constants.s"
	@echo "... error.o"
	@echo "... error.i"
	@echo "... error.s"
	@echo "... field.o"
	@echo "... field.i"
	@echo "... field.s"
	@echo "... log.o"
	@echo "... log.i"
	@echo "... log.s"
	@echo "... query.o"
	@echo "... query.i"
	@echo "... query.s"
	@echo "... row.o"
	@echo "... row.i"
	@echo "... row.s"
	@echo "... test.o"
	@echo "... test.i"
	@echo "... test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

