# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/cmake/linux/x64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/cmake/linux/x64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/libkskyb

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/libkskyb/cmake-build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/home/<USER>/.cache/JetBrains/RemoteDev/dist/dd14f6f94bd7f_CLion-2025.1.3/bin/cmake/linux/x64/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/libkskyb/cmake-build/CMakeFiles /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/libkskyb/cmake-build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/neoatk_bat_216/libsrc/libkskyb/cmake-build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named libqdata

# Build rule for target.
libqdata: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libqdata
.PHONY : libqdata

# fast build rule for target.
libqdata/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libqdata.dir/build.make CMakeFiles/libqdata.dir/build
.PHONY : libqdata/fast

#=============================================================================
# Target rules for targets named libksqueue

# Build rule for target.
libksqueue: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libksqueue
.PHONY : libksqueue

# fast build rule for target.
libksqueue/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueue.dir/build.make CMakeFiles/libksqueue.dir/build
.PHONY : libksqueue/fast

#=============================================================================
# Target rules for targets named libkqueue

# Build rule for target.
libkqueue: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libkqueue
.PHONY : libkqueue

# fast build rule for target.
libkqueue/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkqueue.dir/build.make CMakeFiles/libkqueue.dir/build
.PHONY : libkqueue/fast

#=============================================================================
# Target rules for targets named libkscommon

# Build rule for target.
libkscommon: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libkscommon
.PHONY : libkscommon

# fast build rule for target.
libkscommon/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkscommon.dir/build.make CMakeFiles/libkscommon.dir/build
.PHONY : libkscommon/fast

#=============================================================================
# Target rules for targets named libksconfig

# Build rule for target.
libksconfig: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libksconfig
.PHONY : libksconfig

# fast build rule for target.
libksconfig/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksconfig.dir/build.make CMakeFiles/libksconfig.dir/build
.PHONY : libksconfig/fast

#=============================================================================
# Target rules for targets named libksbase64

# Build rule for target.
libksbase64: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libksbase64
.PHONY : libksbase64

# fast build rule for target.
libksbase64/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksbase64.dir/build.make CMakeFiles/libksbase64.dir/build
.PHONY : libksbase64/fast

#=============================================================================
# Target rules for targets named libksqueuetable

# Build rule for target.
libksqueuetable: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libksqueuetable
.PHONY : libksqueuetable

# fast build rule for target.
libksqueuetable/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueuetable.dir/build.make CMakeFiles/libksqueuetable.dir/build
.PHONY : libksqueuetable/fast

#=============================================================================
# Target rules for targets named libkssocket

# Build rule for target.
libkssocket: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libkssocket
.PHONY : libkssocket

# fast build rule for target.
libkssocket/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkssocket.dir/build.make CMakeFiles/libkssocket.dir/build
.PHONY : libkssocket/fast

#=============================================================================
# Target rules for targets named libksthread

# Build rule for target.
libksthread: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libksthread
.PHONY : libksthread

# fast build rule for target.
libksthread/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksthread.dir/build.make CMakeFiles/libksthread.dir/build
.PHONY : libksthread/fast

#=============================================================================
# Target rules for targets named libksseedbyte

# Build rule for target.
libksseedbyte: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 libksseedbyte
.PHONY : libksseedbyte

# fast build rule for target.
libksseedbyte/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksseedbyte.dir/build.make CMakeFiles/libksseedbyte.dir/build
.PHONY : libksseedbyte/fast

#=============================================================================
# Target rules for targets named all_libkskyb

# Build rule for target.
all_libkskyb: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all_libkskyb
.PHONY : all_libkskyb

# fast build rule for target.
all_libkskyb/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/all_libkskyb.dir/build.make CMakeFiles/all_libkskyb.dir/build
.PHONY : all_libkskyb/fast

#=============================================================================
# Target rules for targets named install_libkskyb

# Build rule for target.
install_libkskyb: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 install_libkskyb
.PHONY : install_libkskyb

# fast build rule for target.
install_libkskyb/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/install_libkskyb.dir/build.make CMakeFiles/install_libkskyb.dir/build
.PHONY : install_libkskyb/fast

#=============================================================================
# Target rules for targets named clean_libkskyb

# Build rule for target.
clean_libkskyb: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean_libkskyb
.PHONY : clean_libkskyb

# fast build rule for target.
clean_libkskyb/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean_libkskyb.dir/build.make CMakeFiles/clean_libkskyb.dir/build
.PHONY : clean_libkskyb/fast

src/Seedx-1.o: src/Seedx-1.c.o
.PHONY : src/Seedx-1.o

# target to build an object file
src/Seedx-1.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksseedbyte.dir/build.make CMakeFiles/libksseedbyte.dir/src/Seedx-1.c.o
.PHONY : src/Seedx-1.c.o

src/Seedx-1.i: src/Seedx-1.c.i
.PHONY : src/Seedx-1.i

# target to preprocess a source file
src/Seedx-1.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksseedbyte.dir/build.make CMakeFiles/libksseedbyte.dir/src/Seedx-1.c.i
.PHONY : src/Seedx-1.c.i

src/Seedx-1.s: src/Seedx-1.c.s
.PHONY : src/Seedx-1.s

# target to generate assembly for a file
src/Seedx-1.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksseedbyte.dir/build.make CMakeFiles/libksseedbyte.dir/src/Seedx-1.c.s
.PHONY : src/Seedx-1.c.s

src/kqueue.o: src/kqueue.c.o
.PHONY : src/kqueue.o

# target to build an object file
src/kqueue.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkqueue.dir/build.make CMakeFiles/libkqueue.dir/src/kqueue.c.o
.PHONY : src/kqueue.c.o

src/kqueue.i: src/kqueue.c.i
.PHONY : src/kqueue.i

# target to preprocess a source file
src/kqueue.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkqueue.dir/build.make CMakeFiles/libkqueue.dir/src/kqueue.c.i
.PHONY : src/kqueue.c.i

src/kqueue.s: src/kqueue.c.s
.PHONY : src/kqueue.s

# target to generate assembly for a file
src/kqueue.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkqueue.dir/build.make CMakeFiles/libkqueue.dir/src/kqueue.c.s
.PHONY : src/kqueue.c.s

src/ksbase64.o: src/ksbase64.cpp.o
.PHONY : src/ksbase64.o

# target to build an object file
src/ksbase64.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksbase64.dir/build.make CMakeFiles/libksbase64.dir/src/ksbase64.cpp.o
.PHONY : src/ksbase64.cpp.o

src/ksbase64.i: src/ksbase64.cpp.i
.PHONY : src/ksbase64.i

# target to preprocess a source file
src/ksbase64.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksbase64.dir/build.make CMakeFiles/libksbase64.dir/src/ksbase64.cpp.i
.PHONY : src/ksbase64.cpp.i

src/ksbase64.s: src/ksbase64.cpp.s
.PHONY : src/ksbase64.s

# target to generate assembly for a file
src/ksbase64.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksbase64.dir/build.make CMakeFiles/libksbase64.dir/src/ksbase64.cpp.s
.PHONY : src/ksbase64.cpp.s

src/kscommon.o: src/kscommon.cpp.o
.PHONY : src/kscommon.o

# target to build an object file
src/kscommon.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkscommon.dir/build.make CMakeFiles/libkscommon.dir/src/kscommon.cpp.o
.PHONY : src/kscommon.cpp.o

src/kscommon.i: src/kscommon.cpp.i
.PHONY : src/kscommon.i

# target to preprocess a source file
src/kscommon.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkscommon.dir/build.make CMakeFiles/libkscommon.dir/src/kscommon.cpp.i
.PHONY : src/kscommon.cpp.i

src/kscommon.s: src/kscommon.cpp.s
.PHONY : src/kscommon.s

# target to generate assembly for a file
src/kscommon.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkscommon.dir/build.make CMakeFiles/libkscommon.dir/src/kscommon.cpp.s
.PHONY : src/kscommon.cpp.s

src/ksconfig.o: src/ksconfig.cpp.o
.PHONY : src/ksconfig.o

# target to build an object file
src/ksconfig.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksconfig.dir/build.make CMakeFiles/libksconfig.dir/src/ksconfig.cpp.o
.PHONY : src/ksconfig.cpp.o

src/ksconfig.i: src/ksconfig.cpp.i
.PHONY : src/ksconfig.i

# target to preprocess a source file
src/ksconfig.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksconfig.dir/build.make CMakeFiles/libksconfig.dir/src/ksconfig.cpp.i
.PHONY : src/ksconfig.cpp.i

src/ksconfig.s: src/ksconfig.cpp.s
.PHONY : src/ksconfig.s

# target to generate assembly for a file
src/ksconfig.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksconfig.dir/build.make CMakeFiles/libksconfig.dir/src/ksconfig.cpp.s
.PHONY : src/ksconfig.cpp.s

src/ksqueue.o: src/ksqueue.cpp.o
.PHONY : src/ksqueue.o

# target to build an object file
src/ksqueue.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueue.dir/build.make CMakeFiles/libksqueue.dir/src/ksqueue.cpp.o
.PHONY : src/ksqueue.cpp.o

src/ksqueue.i: src/ksqueue.cpp.i
.PHONY : src/ksqueue.i

# target to preprocess a source file
src/ksqueue.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueue.dir/build.make CMakeFiles/libksqueue.dir/src/ksqueue.cpp.i
.PHONY : src/ksqueue.cpp.i

src/ksqueue.s: src/ksqueue.cpp.s
.PHONY : src/ksqueue.s

# target to generate assembly for a file
src/ksqueue.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueue.dir/build.make CMakeFiles/libksqueue.dir/src/ksqueue.cpp.s
.PHONY : src/ksqueue.cpp.s

src/ksqueuetable.o: src/ksqueuetable.cpp.o
.PHONY : src/ksqueuetable.o

# target to build an object file
src/ksqueuetable.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueuetable.dir/build.make CMakeFiles/libksqueuetable.dir/src/ksqueuetable.cpp.o
.PHONY : src/ksqueuetable.cpp.o

src/ksqueuetable.i: src/ksqueuetable.cpp.i
.PHONY : src/ksqueuetable.i

# target to preprocess a source file
src/ksqueuetable.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueuetable.dir/build.make CMakeFiles/libksqueuetable.dir/src/ksqueuetable.cpp.i
.PHONY : src/ksqueuetable.cpp.i

src/ksqueuetable.s: src/ksqueuetable.cpp.s
.PHONY : src/ksqueuetable.s

# target to generate assembly for a file
src/ksqueuetable.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksqueuetable.dir/build.make CMakeFiles/libksqueuetable.dir/src/ksqueuetable.cpp.s
.PHONY : src/ksqueuetable.cpp.s

src/ksseedbyte.o: src/ksseedbyte.cpp.o
.PHONY : src/ksseedbyte.o

# target to build an object file
src/ksseedbyte.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksseedbyte.dir/build.make CMakeFiles/libksseedbyte.dir/src/ksseedbyte.cpp.o
.PHONY : src/ksseedbyte.cpp.o

src/ksseedbyte.i: src/ksseedbyte.cpp.i
.PHONY : src/ksseedbyte.i

# target to preprocess a source file
src/ksseedbyte.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksseedbyte.dir/build.make CMakeFiles/libksseedbyte.dir/src/ksseedbyte.cpp.i
.PHONY : src/ksseedbyte.cpp.i

src/ksseedbyte.s: src/ksseedbyte.cpp.s
.PHONY : src/ksseedbyte.s

# target to generate assembly for a file
src/ksseedbyte.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksseedbyte.dir/build.make CMakeFiles/libksseedbyte.dir/src/ksseedbyte.cpp.s
.PHONY : src/ksseedbyte.cpp.s

src/kssocket.o: src/kssocket.cpp.o
.PHONY : src/kssocket.o

# target to build an object file
src/kssocket.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkssocket.dir/build.make CMakeFiles/libkssocket.dir/src/kssocket.cpp.o
.PHONY : src/kssocket.cpp.o

src/kssocket.i: src/kssocket.cpp.i
.PHONY : src/kssocket.i

# target to preprocess a source file
src/kssocket.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkssocket.dir/build.make CMakeFiles/libkssocket.dir/src/kssocket.cpp.i
.PHONY : src/kssocket.cpp.i

src/kssocket.s: src/kssocket.cpp.s
.PHONY : src/kssocket.s

# target to generate assembly for a file
src/kssocket.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libkssocket.dir/build.make CMakeFiles/libkssocket.dir/src/kssocket.cpp.s
.PHONY : src/kssocket.cpp.s

src/ksthread.o: src/ksthread.cpp.o
.PHONY : src/ksthread.o

# target to build an object file
src/ksthread.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksthread.dir/build.make CMakeFiles/libksthread.dir/src/ksthread.cpp.o
.PHONY : src/ksthread.cpp.o

src/ksthread.i: src/ksthread.cpp.i
.PHONY : src/ksthread.i

# target to preprocess a source file
src/ksthread.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksthread.dir/build.make CMakeFiles/libksthread.dir/src/ksthread.cpp.i
.PHONY : src/ksthread.cpp.i

src/ksthread.s: src/ksthread.cpp.s
.PHONY : src/ksthread.s

# target to generate assembly for a file
src/ksthread.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libksthread.dir/build.make CMakeFiles/libksthread.dir/src/ksthread.cpp.s
.PHONY : src/ksthread.cpp.s

src/qdata.o: src/qdata.cpp.o
.PHONY : src/qdata.o

# target to build an object file
src/qdata.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libqdata.dir/build.make CMakeFiles/libqdata.dir/src/qdata.cpp.o
.PHONY : src/qdata.cpp.o

src/qdata.i: src/qdata.cpp.i
.PHONY : src/qdata.i

# target to preprocess a source file
src/qdata.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libqdata.dir/build.make CMakeFiles/libqdata.dir/src/qdata.cpp.i
.PHONY : src/qdata.cpp.i

src/qdata.s: src/qdata.cpp.s
.PHONY : src/qdata.s

# target to generate assembly for a file
src/qdata.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/libqdata.dir/build.make CMakeFiles/libqdata.dir/src/qdata.cpp.s
.PHONY : src/qdata.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... all_libkskyb"
	@echo "... clean_libkskyb"
	@echo "... install_libkskyb"
	@echo "... libkqueue"
	@echo "... libksbase64"
	@echo "... libkscommon"
	@echo "... libksconfig"
	@echo "... libksqueue"
	@echo "... libksqueuetable"
	@echo "... libksseedbyte"
	@echo "... libkssocket"
	@echo "... libksthread"
	@echo "... libqdata"
	@echo "... src/Seedx-1.o"
	@echo "... src/Seedx-1.i"
	@echo "... src/Seedx-1.s"
	@echo "... src/kqueue.o"
	@echo "... src/kqueue.i"
	@echo "... src/kqueue.s"
	@echo "... src/ksbase64.o"
	@echo "... src/ksbase64.i"
	@echo "... src/ksbase64.s"
	@echo "... src/kscommon.o"
	@echo "... src/kscommon.i"
	@echo "... src/kscommon.s"
	@echo "... src/ksconfig.o"
	@echo "... src/ksconfig.i"
	@echo "... src/ksconfig.s"
	@echo "... src/ksqueue.o"
	@echo "... src/ksqueue.i"
	@echo "... src/ksqueue.s"
	@echo "... src/ksqueuetable.o"
	@echo "... src/ksqueuetable.i"
	@echo "... src/ksqueuetable.s"
	@echo "... src/ksseedbyte.o"
	@echo "... src/ksseedbyte.i"
	@echo "... src/ksseedbyte.s"
	@echo "... src/kssocket.o"
	@echo "... src/kssocket.i"
	@echo "... src/kssocket.s"
	@echo "... src/ksthread.o"
	@echo "... src/ksthread.i"
	@echo "... src/ksthread.s"
	@echo "... src/qdata.o"
	@echo "... src/qdata.i"
	@echo "... src/qdata.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

