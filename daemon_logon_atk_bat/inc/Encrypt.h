#ifndef _ENCRYPT_H_
#define _ENCRYPT_H_
#include <stdlib.h>
#include <cstdio>
#include <cstring>
#include <iostream>
using namespace std;

#include <openssl/aes.h>

// OpenSSL 버전별 헤더 포함
#ifdef ROCKY_LINUX_9
    // Rocky Linux 9 (OpenSSL 3.x)
    #include <openssl/modes.h>
    #define OPENSSL_SUPPRESS_DEPRECATED
#elif defined(CENTOS_7X)
    // CentOS 7.x (OpenSSL 1.0.2) - modes.h 지원
    #include <openssl/modes.h>
#elif defined(CENTOS_6X)
    // CentOS 6.x (OpenSSL 1.0.1) - modes.h가 없을 수 있음
    // OpenSSL 1.0.1에서는 modes.h가 없거나 다른 위치에 있을 수 있음
#elif defined(OPENSSL_3X)
    #include <openssl/modes.h>
    #define OPENSSL_SUPPRESS_DEPRECATED
#elif defined(OPENSSL_11X)
    #include <openssl/modes.h>
#else
    // OpenSSL 1.0.x 또는 이전 버전
    // modes.h 헤더가 없을 수 있으므로 포함하지 않음
#endif

#define KEY_SIZE 128
//#define BYTES_SIZE 4096
#define BYTES_SIZE 8192

struct ctr_state {
	unsigned char ivec[AES_BLOCK_SIZE];
	unsigned int num;
	unsigned char ecount[AES_BLOCK_SIZE];
};

class Encrypt 
{
public:
	void set_key();
	void init_ctr(struct ctr_state *state, const unsigned char *iv);
	void encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	void decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	
	
	AES_KEY ase_key;
	unsigned char iv[16];
	unsigned char ckey[16];
};
#endif
