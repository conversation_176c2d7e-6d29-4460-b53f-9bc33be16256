/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <stdafx.h>
#include <string>
#include <set>
#include "senderDbInfo.h"
#include "reportDbInfo.h"

using namespace std;
#include <ml_ctrlsub.h>

extern char _DATALOG[64];

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int connectToOracle(char*, char*);
	int closeFromOracle();
	int commitOracle();
	int rollbackOracle();
	int setMMSTBL(CSenderDbMMSTBL &mms_data);
	int getReportDB(CReportDbInfo &rpt_data);
	int setRPTTBL(CSenderDbMMSRPTTBL &rpt_data);
	char* trim(char* szOrg, int leng);
	int setMMSMSG_ATK_V2(CSenderDbMMSMSG_TALK &que_data);
	int setMMSMSG_ATK_V3(CSenderDbMMSMSG_TALK &que_data);
	int setMMSMSG_ATK_V4(CSenderDbMMSMSG_TALK &que_data);
	int setMMSMSG_ATK_V5(CSenderDbMMSMSG_TALK_V5 &que_data);
	//int getMMSID();
	long long getMMSID();
	int setSendReportData(CSenderDbMMSRPTQUE &rpt_data);
	// 20170621 MMSID SEQ USE	
	int selectSEQ();

private:
	char tmpLog3[1024];
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
};

}

#endif /* DATABASEORA_H_ */
